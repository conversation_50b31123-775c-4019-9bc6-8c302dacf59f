/**
 * Generic notes API service
 * Provides unified API methods for both client and candidate notes
 */

import { apiClient } from './apiService';
import { 
  BaseNote, 
  CreateNoteRequest, 
  UpdateNoteRequest, 
  NoteConfig,
  NOTE_CONFIGS,
  NoteEntityType,
  ClientNote,
  CandidateNote,
  CreateClientNoteRequest,
  CreateCandidateNoteRequest
} from '@/types/note';

/**
 * Generic notes API class
 */
export class NotesApi<T extends BaseNote> {
  private config: NoteConfig;

  constructor(entityType: NoteEntityType) {
    this.config = NOTE_CONFIGS[entityType];
  }

  /**
   * Get all notes for a specific entity
   */
  async getByEntityId(entityId: string): Promise<T[]> {
    try {
      const url = `${this.config.apiEndpoint}/${this.config.entityType}/${entityId}`;
      const response = await apiClient.get(url);
      return response.data;
    } catch (error) {
      console.error(`Error fetching ${this.config.entityType} notes:`, error);
      throw error;
    }
  }

  /**
   * Create a new note
   */
  async create(data: any): Promise<T> {
    try {
      const response = await apiClient.post(this.config.apiEndpoint, data);
      return response.data;
    } catch (error) {
      console.error(`Error creating ${this.config.entityType} note:`, error);
      throw error;
    }
  }

  /**
   * Update an existing note
   */
  async update(id: string, data: UpdateNoteRequest & { user_id: string }): Promise<T> {
    try {
      const response = await apiClient.put(`${this.config.apiEndpoint}/${id}`, data);
      return response.data;
    } catch (error) {
      console.error(`Error updating ${this.config.entityType} note:`, error);
      throw error;
    }
  }

  /**
   * Delete a note
   */
  async delete(id: string, userId: string): Promise<void> {
    try {
      await apiClient.delete(`${this.config.apiEndpoint}/${id}`, {
        data: { user_id: userId }
      });
    } catch (error) {
      console.error(`Error deleting ${this.config.entityType} note:`, error);
      throw error;
    }
  }
}

/**
 * Client notes API instance
 */
export const clientNotesApi = {
  /**
   * Get all notes for a specific client
   */
  getByClientId: async (clientId: string): Promise<ClientNote[]> => {
    const api = new NotesApi<ClientNote>('client');
    return api.getByEntityId(clientId);
  },

  /**
   * Create a new client note
   */
  create: async (data: CreateClientNoteRequest & { user_id: string }): Promise<ClientNote> => {
    const api = new NotesApi<ClientNote>('client');
    return api.create(data);
  },

  /**
   * Update an existing client note
   */
  update: async (id: string, data: UpdateNoteRequest & { user_id: string }): Promise<ClientNote> => {
    const api = new NotesApi<ClientNote>('client');
    return api.update(id, data);
  },

  /**
   * Delete a client note
   */
  delete: async (id: string, userId: string): Promise<void> => {
    const api = new NotesApi<ClientNote>('client');
    return api.delete(id, userId);
  }
};

/**
 * Enhanced candidate notes API (maintains backward compatibility)
 */
export const candidateNotesApi = {
  /**
   * Get all notes for a specific candidate
   */
  getByCandidateId: async (candidateId: string): Promise<CandidateNote[]> => {
    const api = new NotesApi<CandidateNote>('candidate');
    return api.getByEntityId(candidateId);
  },

  /**
   * Create a new candidate note
   */
  create: async (data: CreateCandidateNoteRequest & { user_id: string }): Promise<CandidateNote> => {
    const api = new NotesApi<CandidateNote>('candidate');
    return api.create(data);
  },

  /**
   * Update an existing candidate note
   */
  update: async (id: string, data: UpdateNoteRequest & { user_id: string }): Promise<CandidateNote> => {
    const api = new NotesApi<CandidateNote>('candidate');
    return api.update(id, data);
  },

  /**
   * Delete a candidate note
   */
  delete: async (id: string, userId: string): Promise<void> => {
    const api = new NotesApi<CandidateNote>('candidate');
    return api.delete(id, userId);
  }
};

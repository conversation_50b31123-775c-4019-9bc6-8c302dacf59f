
import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import DashboardLayout from "@/components/layout/DashboardLayout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Candidate, CandidateStatus, Communication } from "@/types/candidate";
import {
  getSecondaryStatusLabel,
  getPrimaryStatusLabel,
  SECONDARY_STATUS_OPTIONS,
  PrimaryStatus
} from "@/lib/constants/candidateStatus";
import {
  ArrowLeft,
  Calendar,
  Mail,
  Phone,
  User,
  Link as LinkIcon,
  ExternalLink,
  MessageSquare as WhatsappIcon,
  Loader2,
  Edit,
  Sprout
} from "lucide-react";
import { createExternalLinkProps } from "@/utils/urlUtils";
import CandidatePipeline from "@/components/candidates/CandidatePipeline";
import { candidatesApi } from "@/services/apiService";
import candidateService from "@/services/candidateService";
import AssessmentCard from "@/components/candidates/AssessmentCard";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import CandidateForm from "@/components/candidates/CandidateForm";
import { CandidateNoteCreator } from "@/components/candidates/CandidateNoteCreator";
import { NotesTimeline } from "@/components/notes/NotesTimeline";
import { useCandidateNotes } from "@/hooks/useNotes";
import { CandidateNote } from "@/types/note";

// Email templates
const emailTemplates = [
  {
    id: "e1",
    name: "Interview Invitation",
    subject: "Interview Invitation - [Position]",
    content: "Dear [Candidate Name],\n\nWe are pleased to invite you for an interview for the [Position] role. The interview is scheduled for [Date and Time].\n\nPlease let us know if this works for you.\n\nBest regards,\n[Recruiter Name]"
  },
  {
    id: "e2",
    name: "Application Acknowledgment",
    subject: "Application Received - [Position]",
    content: "Dear [Candidate Name],\n\nThank you for applying to the [Position] role at our company. We have received your application and are currently reviewing it.\n\nWe will be in touch soon.\n\nBest regards,\n[Recruiter Name]"
  },
  {
    id: "e3",
    name: "Assessment Request",
    subject: "Technical Assessment - [Position]",
    content: "Dear [Candidate Name],\n\nAs part of our evaluation process, we'd like you to complete a technical assessment. Please find the details attached.\n\nDeadline: [Deadline]\n\nBest regards,\n[Recruiter Name]"
  },
  {
    id: "e4",
    name: "Offer Letter",
    subject: "Job Offer - [Position]",
    content: "Dear [Candidate Name],\n\nWe are delighted to offer you the position of [Position] at our company. Please find the detailed offer attached.\n\nPlease let us know your decision by [Deadline].\n\nBest regards,\n[Recruiter Name]"
  }
];

export default function CandidateDetails() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [candidate, setCandidate] = useState<Candidate | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [emailSubject, setEmailSubject] = useState("");
  const [emailContent, setEmailContent] = useState("");
  const [selectedTemplate, setSelectedTemplate] = useState("");
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState("basic");
  const [notesCount, setNotesCount] = useState(0);
  const [lastAddedNoteId, setLastAddedNoteId] = useState<string | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const { toast } = useToast();

  // Use the unified notes system
  const {
    notes: candidateNotes,
    isLoading: notesLoading,
    updateNote,
    deleteNote,
  } = useCandidateNotes(candidate?.id || '');

  // Helper function to display field values with N/A for empty values
  const displayValue = (value: any, type: 'text' | 'number' | 'date' | 'array' = 'text'): string => {
    if (value === null || value === undefined || value === '' || (Array.isArray(value) && value.length === 0)) {
      return 'N/A';
    }

    switch (type) {
      case 'number':
        // Handle both numeric values and numeric strings from PostgreSQL
        if (typeof value === 'number') {
          return value.toString();
        }
        if (typeof value === 'string' && !isNaN(Number(value)) && value.trim() !== '') {
          return Number(value).toString();
        }
        return 'N/A';
      case 'date':
        return value ? new Date(value).toLocaleDateString() : 'N/A';
      case 'array':
        return Array.isArray(value) && value.length > 0 ? value.join(', ') : 'N/A';
      default:
        return value.toString();
    }
  };

  // Helper component for field display
  const FieldDisplay = ({ label, value, type = 'text' }: { label: string; value: any; type?: 'text' | 'number' | 'date' | 'array' }) => {
    const displayedValue = displayValue(value, type);
    const isEmpty = displayedValue === 'N/A';

    return (
      <div className="space-y-1">
        <label className="text-sm font-medium text-muted-foreground">{label}</label>
        <p className={`text-sm ${isEmpty ? 'text-muted-foreground italic' : ''}`}>
          {displayedValue}
        </p>
      </div>
    );
  };

  // Fetch candidate data when component mounts
  useEffect(() => {
    const fetchCandidate = async () => {
      if (!id) {
        setError("No candidate ID provided");
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const data = await candidatesApi.getById(id);



        if (!data) {
          setError("Candidate not found");
          setIsLoading(false);
          return;
        }

        // Transform the data from snake_case to camelCase - Include ALL database fields
        const formattedCandidate: Candidate = {
          id: data.id,
          name: `${data.first_name} ${data.last_name}`,
          firstName: data.first_name,
          lastName: data.last_name,
          email: data.email,
          phone: data.phone || '',
          position: data.current_position || '',
          status: (data.status as CandidateStatus) || PrimaryStatus.NEW,
          secondaryStatus: data.secondary_status || '',
          appliedDate: new Date(data.created_at).toLocaleDateString(),
          inTalentPool: false, // Default value
          isInIncubator: data.is_in_incubator || false,
          communications: data.communications || [],
          assessments: data.assessments || [],
          courses: data.courses || [],
          projects: data.projects || [],
          notes: data.notes || '',
          resume: data.resume_url || '',
          coverLetter: data.cover_letter_url || '',
          portfolio: data.portfolio_url || '',
          stargetyId: data.stargety_id || '',
          location: data.location || '',

          // Professional Information
          currentCompany: data.current_company || '',
          currentPosition: data.current_position || '',
          experienceYears: data.experience_years ? Number(data.experience_years) : null,
          education: data.education || '',
          desiredSalary: data.desired_salary ? Number(data.desired_salary) : null,
          salaryCurrency: data.salary_currency || 'USD',
          availabilityDate: data.availability_date || '',
          source: data.source || '',
          skills: data.skills || [],

          // Assessment & Interview Information
          englishLevel: data.english_level || '',
          interviewScore: data.interview_score ? Number(data.interview_score) : null,
          interviewNotes: data.interview_notes || '',
          challenge: data.challenge || '',
          challengeNotes: data.challenge_notes || '',
          challengeFeedback: data.challenge_feedback || '',
          driveScore: data.drive_score ? Number(data.drive_score) : null,
          resilienceScore: data.resilience_score ? Number(data.resilience_score) : null,
          collaborationScore: data.collaboration_score ? Number(data.collaboration_score) : null,
          result: data.result || '',

          // Process Information
          isDuplicate: data.is_duplicate || 'new',

          // Social Links
          socialLinks: {
            linkedin: data.linkedin_url || '',
            github: data.github_url || '',
            twitter: data.twitter_url || '',
          },
          twitterUrl: data.twitter_url || '',

          // Timestamps
          createdAt: data.created_at,
          updatedAt: data.updated_at,

          intakeResponses: data.intake_responses || [
            { question: "Years of experience", answer: data.experience_years ? `${data.experience_years} years` : 'Not specified' },
            { question: "Education", answer: data.education || 'Not specified' },
            { question: "Current company", answer: data.current_company || 'Not specified' },
            { question: "Desired salary", answer: data.desired_salary ? `${data.salary_currency || 'USD'} ${data.desired_salary}` : 'Not specified' },
            { question: "Availability date", answer: data.availability_date ? new Date(data.availability_date).toLocaleDateString() : 'Not specified' },
            { question: "Source", answer: data.source || 'Not specified' },
          ],
        };



        setCandidate(formattedCandidate);
      } catch (err) {
        console.error("Error fetching candidate:", err);
        setError("Failed to load candidate data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchCandidate();
  }, [id]);

  const handleStatusChange = async (status: CandidateStatus) => {
    if (!candidate) return;

    try {
      // Get default secondary status for the new primary status
      const secondaryStatusOptions = SECONDARY_STATUS_OPTIONS[status as PrimaryStatus] || [];
      const defaultSecondaryStatus = secondaryStatusOptions.length > 0 ? secondaryStatusOptions[0].value : '';

      // Update the status in the API
      await candidatesApi.update(candidate.id, {
        status,
        secondary_status: defaultSecondaryStatus
      });

      // Update the local state
      setCandidate(prev => prev ? {
        ...prev,
        status,
        secondaryStatus: defaultSecondaryStatus
      } : null);

      toast({
        title: "Status updated",
        description: `Candidate status changed to ${getPrimaryStatusLabel(status as PrimaryStatus)}`,
      });
    } catch (err) {
      console.error("Error updating candidate status:", err);
      toast({
        title: "Error",
        description: "Failed to update candidate status",
        variant: "destructive",
      });
    }
  };

  const handleEditAssessment = (assessmentId: string) => {
    console.log(`Edit assessment: ${assessmentId}`);
  };

  const handleAddToIncubator = async () => {
    if (!candidate || !id) return;

    try {
      // Update the candidate's incubator status in the database
      await candidatesApi.update(candidate.id, {
        is_in_incubator: true
      });

      // Update the local state
      setCandidate(prev => prev ? {
        ...prev,
        isInIncubator: true
      } : null);

      toast({
        title: "Added to Talent Incubator",
        description: "Candidate has been successfully added to the Talent Incubator program",
      });
    } catch (err) {
      console.error("Error adding candidate to incubator:", err);
      toast({
        title: "Error",
        description: "Failed to add candidate to Talent Incubator",
        variant: "destructive",
      });
    }
  };



  const handleSendEmail = () => {
    if (!emailSubject.trim() || !emailContent.trim()) {
      toast({
        title: "Error",
        description: "Please fill out both subject and content",
        variant: "destructive"
      });
      return;
    }

    const newCommunication: Communication = {
      id: `c${candidate.communications.length + 1}`,
      type: "email",
      date: new Date().toLocaleDateString("en-US", { month: "short", day: "numeric", year: "numeric" }),
      content: emailContent,
      subject: emailSubject,
      sender: "recruiter"
    };

    setCandidate(prev => ({
      ...prev,
      communications: [...prev.communications, newCommunication]
    }));

    setEmailSubject("");
    setEmailContent("");

    toast({
      title: "Email sent",
      description: "Your email has been sent to the candidate",
    });
  };

  const handleTemplateSelect = (templateId: string) => {
    if (!candidate) return;

    const template = emailTemplates.find(t => t.id === templateId);
    if (template) {
      // Replace placeholders with actual values
      let content = template.content
        .replace("[Candidate Name]", candidate.name)
        .replace("[Position]", candidate.position || "")
        .replace("[Recruiter Name]", "Your Name");

      let subject = template.subject
        .replace("[Position]", candidate.position || "");

      setEmailSubject(subject);
      setEmailContent(content);
      setSelectedTemplate(templateId);
    }
  };

  const handleEnhanceWithAI = () => {
    // This would integrate with an AI service
    toast({
      title: "AI Enhancement",
      description: "Email enhanced with AI suggestions",
    });

    // For demo purposes, simulate AI enhancement
    setEmailContent(prev =>
      prev + "\n\n[AI enhanced: Added personalized touch based on candidate's portfolio and experience]"
    );
  };

  const openWhatsApp = () => {
    if (candidate.phone) {
      // Format phone number - remove spaces, parentheses, etc.
      const formattedPhone = candidate.phone.replace(/\D/g, "");
      window.open(`https://wa.me/${formattedPhone}`, "_blank");
    } else {
      toast({
        title: "Error",
        description: "No phone number available for this candidate",
        variant: "destructive"
      });
    }
  };

  const handleEditCandidate = () => {
    setIsEditDialogOpen(true);
  };

  const handleUpdateCandidate = async (data: any) => {
    if (!candidate || !id) return;

    try {
      setIsSaving(true);

      // Prepare candidate data for update - include all new fields
      const candidateData = {
        // Basic Information
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone,
        location: data.location,

        // Professional Information
        position: data.position,
        currentCompany: data.currentCompany,
        experienceYears: data.experienceYears,
        education: data.education,
        desiredSalary: data.desiredSalary,
        salaryCurrency: data.salaryCurrency,
        availabilityDate: data.availabilityDate,
        source: data.source,
        skills: data.skills,

        // Status Information
        status: data.status,
        secondaryStatus: data.secondaryStatus,
        englishLevel: data.englishLevel,

        // URLs and Links
        portfolio: data.portfolio,
        resumeUrl: data.resumeUrl,
        coverLetterUrl: data.coverLetterUrl,
        socialLinks: data.socialLinks,

        // Interview & Assessment
        interviewScore: data.interviewScore,
        interviewNotes: data.interviewNotes,
        challenge: data.challenge,
        challengeNotes: data.challengeNotes,
        challengeFeedback: data.challengeFeedback,

        // Scoring
        driveScore: data.driveScore,
        resilienceScore: data.resilienceScore,
        collaborationScore: data.collaborationScore,

        // Additional Fields
        notes: data.notes,
        stargetyId: data.stargetyId,
        isDuplicate: data.isDuplicate,
        result: data.result
      };

      // Update candidate using the service
      await candidateService.updateCandidate(id, candidateData);

      // Refresh candidate data
      const updatedCandidate = await candidatesApi.getById(id);

      // Transform the data from snake_case to camelCase - Include ALL database fields
      const formattedCandidate: Candidate = {
        id: updatedCandidate.id,
        name: `${updatedCandidate.first_name} ${updatedCandidate.last_name}`,
        firstName: updatedCandidate.first_name,
        lastName: updatedCandidate.last_name,
        email: updatedCandidate.email,
        phone: updatedCandidate.phone || '',
        position: updatedCandidate.current_position || '',
        status: (updatedCandidate.status as CandidateStatus) || PrimaryStatus.NEW,
        secondaryStatus: updatedCandidate.secondary_status || '',
        appliedDate: new Date(updatedCandidate.created_at).toLocaleDateString(),
        inTalentPool: false, // Default value
        isInIncubator: updatedCandidate.is_in_incubator || false,
        communications: updatedCandidate.communications || [],
        assessments: updatedCandidate.assessments || [],
        courses: updatedCandidate.courses || [],
        projects: updatedCandidate.projects || [],
        notes: updatedCandidate.notes || '',
        resume: updatedCandidate.resume_url || '',
        coverLetter: updatedCandidate.cover_letter_url || '',
        portfolio: updatedCandidate.portfolio_url || '',
        stargetyId: updatedCandidate.stargety_id || '',
        location: updatedCandidate.location || '',

        // Professional Information
        currentCompany: updatedCandidate.current_company || '',
        currentPosition: updatedCandidate.current_position || '',
        experienceYears: updatedCandidate.experience_years ? Number(updatedCandidate.experience_years) : null,
        education: updatedCandidate.education || '',
        desiredSalary: updatedCandidate.desired_salary ? Number(updatedCandidate.desired_salary) : null,
        salaryCurrency: updatedCandidate.salary_currency || 'USD',
        availabilityDate: updatedCandidate.availability_date || '',
        source: updatedCandidate.source || '',
        skills: updatedCandidate.skills || [],

        // Assessment & Interview Information
        englishLevel: updatedCandidate.english_level || '',
        interviewScore: updatedCandidate.interview_score ? Number(updatedCandidate.interview_score) : null,
        interviewNotes: updatedCandidate.interview_notes || '',
        challenge: updatedCandidate.challenge || '',
        challengeNotes: updatedCandidate.challenge_notes || '',
        challengeFeedback: updatedCandidate.challenge_feedback || '',
        driveScore: updatedCandidate.drive_score ? Number(updatedCandidate.drive_score) : null,
        resilienceScore: updatedCandidate.resilience_score ? Number(updatedCandidate.resilience_score) : null,
        collaborationScore: updatedCandidate.collaboration_score ? Number(updatedCandidate.collaboration_score) : null,
        result: updatedCandidate.result || '',

        // Process Information
        isDuplicate: updatedCandidate.is_duplicate || 'new',

        // Social Links
        socialLinks: {
          linkedin: updatedCandidate.linkedin_url || '',
          github: updatedCandidate.github_url || '',
          twitter: updatedCandidate.twitter_url || '',
        },

        // Timestamps
        createdAt: updatedCandidate.created_at,
        updatedAt: updatedCandidate.updated_at,

        intakeResponses: updatedCandidate.intake_responses || [
          { question: "Years of experience", answer: updatedCandidate.experience_years ? `${updatedCandidate.experience_years} years` : 'Not specified' },
          { question: "Education", answer: updatedCandidate.education || 'Not specified' },
          { question: "Current company", answer: updatedCandidate.current_company || 'Not specified' },
          { question: "Desired salary", answer: updatedCandidate.desired_salary ? `${updatedCandidate.salary_currency || 'USD'} ${updatedCandidate.desired_salary}` : 'Not specified' },
          { question: "Availability date", answer: updatedCandidate.availability_date ? new Date(updatedCandidate.availability_date).toLocaleDateString() : 'Not specified' },
          { question: "Source", answer: updatedCandidate.source || 'Not specified' },
        ],
      };

      setCandidate(formattedCandidate);
      setIsEditDialogOpen(false);

      toast({
        title: "Success",
        description: "Candidate updated successfully",
      });
    } catch (error) {
      console.error("Error updating candidate:", error);
      toast({
        title: "Error",
        description: "Failed to update candidate. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle loading state
  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center h-[calc(100vh-200px)]">
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
          <p className="text-muted-foreground">Loading candidate details...</p>
        </div>
      </DashboardLayout>
    );
  }

  // Handle error state
  if (error || !candidate) {
    return (
      <DashboardLayout>
        <div className="mb-6">
          <Link to="/candidates">
            <Button variant="ghost" className="gap-2 mb-4">
              <ArrowLeft className="h-4 w-4" />
              Back to Candidates
            </Button>
          </Link>

          <Alert variant="destructive" className="mt-4">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error || "Candidate not found"}. Please try again or contact support if the issue persists.
            </AlertDescription>
          </Alert>

          <Button
            variant="default"
            className="mt-4"
            onClick={() => navigate("/candidates")}
          >
            Return to Candidates List
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="mb-6">
        <Link to="/candidates">
          <Button variant="ghost" className="gap-2 mb-4">
            <ArrowLeft className="h-4 w-4" />
            Back to Candidates
          </Button>
        </Link>

        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex items-center gap-4">
            <div className="relative">
              <div className={`h-16 w-16 rounded-full bg-secondary flex items-center justify-center overflow-hidden ${
                candidate.isInIncubator ? 'ring-4 ring-amber-400 ring-offset-2' : ''
              }`}>
                {candidate.imageUrl ? (
                  <img src={candidate.imageUrl} alt={candidate.name} className="h-full w-full object-cover" />
                ) : (
                  <User className="h-8 w-8 text-muted-foreground" />
                )}
              </div>
              {candidate.isInIncubator && (
                <div className="absolute -bottom-1 -right-1 h-6 w-6 bg-amber-400 rounded-full flex items-center justify-center border-2 border-white">
                  <Sprout className="h-5 w-5 text-amber-600" />
                </div>
              )}
            </div>
            <div>
              <h1 className="text-2xl font-bold">{candidate.name}</h1>
              <p className="text-muted-foreground">
                {candidate.position ? candidate.position : <span className="italic text-muted-foregound">Position not specified</span>}
              </p>
              <div className="flex items-center gap-3 mt-1 text-sm">
                <span className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  Applied {candidate.appliedDate}
                </span>
                <div className="flex flex-col">
                  <Badge variant="outline" className="capitalize mb-1">
                    {getPrimaryStatusLabel(candidate.status as PrimaryStatus)}
                  </Badge>
                  {candidate.secondaryStatus && (
                    <span className="text-xs text-muted-foreground">
                      {getSecondaryStatusLabel(candidate.secondaryStatus)}
                    </span>
                  )}
                </div>
                {candidate.location && (
                  <span className="flex items-center gap-1">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    {candidate.location}
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            <Button onClick={handleEditCandidate} className="gap-2" variant="default">
              <Edit className="h-4 w-4" />
              Edit Candidate
            </Button>

            {!candidate.isInIncubator && (
              <Button onClick={handleAddToIncubator} variant="secondary">
                Add to Talent Incubator
              </Button>
            )}
          </div>

          {/* Edit Candidate Dialog */}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Edit Candidate: {candidate.name}</DialogTitle>
                <DialogDescription>
                  Update candidate information using the form below.
                </DialogDescription>
              </DialogHeader>

              <CandidateForm
                candidate={candidate}
                onSubmit={handleUpdateCandidate}
                isLoading={isSaving}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4 grid w-full grid-cols-6">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="professional">Professional</TabsTrigger>
              <TabsTrigger value="social">Social & Portfolio</TabsTrigger>
              <TabsTrigger value="assessment">Assessment</TabsTrigger>
              <TabsTrigger value="process">Process</TabsTrigger>
              <TabsTrigger value="intake">Intake Responses</TabsTrigger>
            </TabsList>

            <TabsContent value="basic">
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                  <CardDescription>Core candidate details and contact information</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <FieldDisplay label="First Name" value={candidate.firstName} />
                    <FieldDisplay label="Last Name" value={candidate.lastName} />
                    <FieldDisplay label="Email" value={candidate.email} />
                    <FieldDisplay label="Phone" value={candidate.phone} />
                    <FieldDisplay label="Location" value={candidate.location} />
                    <FieldDisplay label="Stargety ID" value={candidate.stargetyId} />
                  </div>

                  <Separator />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FieldDisplay label="Applied Date" value={candidate.appliedDate} />
                    <FieldDisplay label="Created At" value={candidate.createdAt} type="date" />
                    <FieldDisplay label="Updated At" value={candidate.updatedAt} type="date" />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="professional">
              <Card>
                <CardHeader>
                  <CardTitle>Professional Information</CardTitle>
                  <CardDescription>Work experience, education, and career details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FieldDisplay label="Current Position" value={candidate.position} />
                    <FieldDisplay label="Current Company" value={candidate.currentCompany} />
                    <FieldDisplay label="Years of Experience" value={candidate.experienceYears} type="number" />
                    <FieldDisplay label="Education" value={candidate.education} />
                  </div>

                  <Separator />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FieldDisplay label="Desired Salary" value={candidate.desiredSalary ? `${candidate.salaryCurrency} ${candidate.desiredSalary}` : null} />
                    <FieldDisplay label="Availability Date" value={candidate.availabilityDate} type="date" />
                    <FieldDisplay label="Source" value={candidate.source} />
                    <FieldDisplay label="English Level" value={candidate.englishLevel} />
                  </div>

                  <Separator />

                  <div>
                    <FieldDisplay label="Skills" value={candidate.skills} type="array" />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="social">
              <Card>
                <CardHeader>
                  <CardTitle>Social Links & Portfolio</CardTitle>
                  <CardDescription>Online presence and portfolio information</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <label className="text-sm font-medium text-muted-foreground">LinkedIn</label>
                      {candidate.socialLinks?.linkedin ? (
                        <a {...createExternalLinkProps(candidate.socialLinks.linkedin)} className="flex items-center gap-1 text-sm text-blue-600 hover:underline">
                          <LinkIcon className="h-4 w-4" />
                          LinkedIn Profile
                        </a>
                      ) : (
                        <p className="text-sm text-muted-foreground italic">N/A</p>
                      )}
                    </div>

                    <div className="space-y-1">
                      <label className="text-sm font-medium text-muted-foreground">GitHub</label>
                      {candidate.socialLinks?.github ? (
                        <a {...createExternalLinkProps(candidate.socialLinks.github)} className="flex items-center gap-1 text-sm text-gray-600 hover:underline">
                          <LinkIcon className="h-4 w-4" />
                          GitHub Profile
                        </a>
                      ) : (
                        <p className="text-sm text-muted-foreground italic">N/A</p>
                      )}
                    </div>

                    <div className="space-y-1">
                      <label className="text-sm font-medium text-muted-foreground">Twitter</label>
                      {candidate.socialLinks?.twitter ? (
                        <a {...createExternalLinkProps(candidate.socialLinks.twitter)} className="flex items-center gap-1 text-sm text-blue-400 hover:underline">
                          <LinkIcon className="h-4 w-4" />
                          Twitter Profile
                        </a>
                      ) : (
                        <p className="text-sm text-muted-foreground italic">N/A</p>
                      )}
                    </div>

                    <div className="space-y-1">
                      <label className="text-sm font-medium text-muted-foreground">Portfolio</label>
                      {candidate.portfolio ? (
                        <a {...createExternalLinkProps(candidate.portfolio)} className="flex items-center gap-1 text-sm text-purple-600 hover:underline">
                          <ExternalLink className="h-4 w-4" />
                          Portfolio Website
                        </a>
                      ) : (
                        <p className="text-sm text-muted-foreground italic">N/A</p>
                      )}
                    </div>
                  </div>

                  <Separator />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FieldDisplay label="Resume URL" value={candidate.resume} />
                    <FieldDisplay label="Cover Letter URL" value={candidate.coverLetter} />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="assessment">
              <Card>
                <CardHeader>
                  <CardTitle>Assessment & Interview Information</CardTitle>
                  <CardDescription>Interview scores, challenges, and feedback details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <FieldDisplay label="Interview Score" value={candidate.interviewScore} type="number" />
                    <FieldDisplay label="Drive Score" value={candidate.driveScore} type="number" />
                    <FieldDisplay label="Resilience Score" value={candidate.resilienceScore} type="number" />
                    <FieldDisplay label="Collaboration Score" value={candidate.collaborationScore} type="number" />
                  </div>

                  <Separator />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FieldDisplay label="Interview Notes" value={candidate.interviewNotes} />
                    <FieldDisplay label="Challenge" value={candidate.challenge} />
                    <FieldDisplay label="Challenge Notes" value={candidate.challengeNotes} />
                    <FieldDisplay label="Challenge Feedback" value={candidate.challengeFeedback} />
                  </div>

                  <Separator />

                  <div>
                    <FieldDisplay label="Final Result" value={candidate.result} />
                  </div>

                  {candidate.assessments && candidate.assessments.length > 0 && (
                    <>
                      <Separator />
                      <div>
                        <h3 className="text-md font-semibold mb-3">Assessment Progress</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          {candidate.assessments.map(assessment => (
                            <AssessmentCard
                              key={assessment.id}
                              assessment={assessment}
                              onEdit={handleEditAssessment}
                            />
                          ))}
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="process">
              <Card>
                <CardHeader>
                  <CardTitle>Process Information</CardTitle>
                  <CardDescription>Status, workflow, and process tracking details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <label className="text-sm font-medium text-muted-foreground">Primary Status</label>
                      <Badge variant="outline" className="capitalize">
                        {getPrimaryStatusLabel(candidate.status as PrimaryStatus)}
                      </Badge>
                    </div>
                    <div className="space-y-1">
                      <label className="text-sm font-medium text-muted-foreground">Secondary Status</label>
                      <p className={`text-sm ${candidate.secondaryStatus ? '' : 'text-muted-foreground italic'}`}>
                        {candidate.secondaryStatus ? getSecondaryStatusLabel(candidate.secondaryStatus) : 'N/A'}
                      </p>
                    </div>
                    <FieldDisplay label="Duplicate Status" value={candidate.isDuplicate} />
                  </div>

                  <Separator />

                  <div>
                    <h3 className="text-md font-semibold mb-3">Pipeline Status</h3>
                    <CandidatePipeline
                      currentStatus={candidate.status}
                      secondaryStatus={candidate.secondaryStatus}
                      onStatusChange={handleStatusChange}
                    />
                  </div>

                  <Separator />

                  <div>
                    <FieldDisplay label="Notes" value={candidate.notes} />
                  </div>

                  <Separator />

                  <div>
                    <NotesTimeline<CandidateNote>
                      entityType="candidate"
                      entityId={candidate.id}
                      notes={candidateNotes}
                      isLoading={notesLoading}
                      onUpdateNote={updateNote}
                      onDeleteNote={deleteNote}
                      highlightedNoteId={lastAddedNoteId}
                      showCreateInterface={false}
                      onNotesCountChange={(count) => setNotesCount(count)}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="intake">
              <Card>
                <CardHeader>
                  <CardTitle>Intake Form Responses</CardTitle>
                  <CardDescription>Responses from the candidate intake form</CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Question</TableHead>
                        <TableHead>Answer</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {candidate.intakeResponses?.map((response, index) => {
                        // Check if the answer might be a URL
                        const isUrl = typeof response.answer === 'string' &&
                          (response.answer.includes('http') ||
                           response.answer.includes('www.') ||
                           /\.[a-z]{2,}\//.test(response.answer));

                        return (
                          <TableRow key={index}>
                            <TableCell className="font-medium">{response.question}</TableCell>
                            <TableCell
                              contentType={isUrl ? 'url' : undefined}
                              maxWidth="300px"
                            >
                              {response.answer}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div>
          <Card id="quickActions">
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full justify-start" variant="ghost" onClick={openWhatsApp}>
                <WhatsappIcon className="h-4 w-4 mr-2" />
                Message on WhatsApp
              </Button>

              <Dialog>
                <DialogTrigger asChild>
                  <Button className="w-full justify-start" variant="ghost">
                    <Mail className="h-4 w-4 mr-2" />
                    Send Email
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-3xl">
                  <DialogHeader>
                    <DialogTitle>Send Email to {candidate.name}</DialogTitle>
                    <DialogDescription>
                      Compose an email to send to the candidate.
                    </DialogDescription>
                  </DialogHeader>

                  <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                      <Label htmlFor="template">Select Template</Label>
                      <select
                        id="template"
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                        value={selectedTemplate}
                        onChange={(e) => handleTemplateSelect(e.target.value)}
                      >
                        <option value="">-- Select a template --</option>
                        {emailTemplates.map(template => (
                          <option key={template.id} value={template.id}>
                            {template.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor="email-subject">Subject</Label>
                      <Input
                        id="email-subject"
                        value={emailSubject}
                        onChange={(e) => setEmailSubject(e.target.value)}
                        placeholder="Email subject"
                      />
                    </div>

                    <div className="grid gap-2">
                      <div className="flex justify-between items-center">
                        <Label htmlFor="email-content">Content</Label>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={handleEnhanceWithAI}
                        >
                          Enhance with AI
                        </Button>
                      </div>
                      <Textarea
                        id="email-content"
                        value={emailContent}
                        onChange={(e) => setEmailContent(e.target.value)}
                        placeholder="Write your email content here..."
                        className="min-h-[200px]"
                      />
                    </div>
                  </div>

                  <DialogFooter>
                    <Button type="button" variant="default" onClick={handleSendEmail}>Send Email</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <Button className="w-full justify-start" variant="ghost">
                <Phone className="h-4 w-4 mr-2" />
                Schedule Call
              </Button>
              <Button className="w-full justify-start" variant="ghost">
                <Calendar className="h-4 w-4 mr-2" />
                Schedule Interview
              </Button>
            </CardContent>
          </Card>

          <div className="mt-6">
            <CandidateNoteCreator
              candidateId={candidate.id}
              notesCount={notesCount}
              onNoteAdded={(noteId) => {
                setLastAddedNoteId(noteId);

                // Trigger refresh of the timeline
                setRefreshTrigger(prev => prev + 1);

                // Switch to process tab and scroll to the new note
                setActiveTab('process');

                // Scroll to the timeline after a short delay to allow tab switching and refresh
                setTimeout(() => {
                  const timelineElement = document.querySelector('[data-timeline-container]');
                  if (timelineElement) {
                    timelineElement.scrollIntoView({
                      behavior: 'smooth',
                      block: 'start'
                    });
                  }
                }, 300);
              }}
            />
          </div>

    {candidate.isInIncubator && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                   <Sprout className="h-5 w-5"/>
                  Talent Incubator Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-2 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                    <div className="h-2 w-2 bg-amber-400 rounded-full animate-pulse"></div>
                    <span className="text-sm font-medium text-amber-800">
                      Active in Talent Incubator Program
                    </span>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Development Progress</span>
                      <span className="font-medium">In Progress</span>
                    </div>
                    <div className="w-full bg-secondary rounded-full h-2">
                      <div className="bg-amber-400 h-2 rounded-full" style={{ width: '25%' }}></div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Growth Milestones</span>
                      <span className="font-medium">1/4 completed</span>
                    </div>
                    <div className="w-full bg-secondary rounded-full h-2">
                      <div className="bg-amber-400 h-2 rounded-full" style={{ width: '25%' }}></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}



        </div>
      </div>
    </DashboardLayout>
  );
}
